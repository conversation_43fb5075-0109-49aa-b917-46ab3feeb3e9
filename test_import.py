#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف الاستيراد
"""

import sys
import os
import csv

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.database import db
from models.products import ProductModel
from models.customers import CustomerModel

def test_import_products():
    """اختبار استيراد المنتجات"""
    print("اختبار استيراد المنتجات...")
    
    # الاتصال بقاعدة البيانات
    if not db.connect():
        print("فشل الاتصال بقاعدة البيانات")
        return False
    
    file_name = "sample_products.csv"
    
    if not os.path.exists(file_name):
        print(f"ملف الاختبار غير موجود: {file_name}")
        return False
    
    imported_count = 0
    updated_count = 0
    error_count = 0
    errors = []
    
    try:
        # قراءة ملف CSV
        with open(file_name, 'r', encoding='utf-8-sig') as csvfile:
            # محاولة تحديد الفاصل تلقائياً
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(csvfile, delimiter=delimiter)
            
            for row_num, row in enumerate(reader, start=2):  # البدء من الصف 2 (بعد العناوين)
                try:
                    # تنظيف البيانات وتحويلها
                    product_data = {}
                    
                    # معالجة الحقول المختلفة (العربية والإنجليزية)
                    name_fields = ['اسم المنتج', 'name', 'product_name', 'Name', 'Product Name']
                    code_fields = ['الكود', 'code', 'product_code', 'Code', 'Product Code']
                    price_fields = ['سعر البيع', 'price', 'sell_price', 'Price', 'Sell Price']
                    cost_fields = ['سعر التكلفة', 'cost', 'cost_price', 'Cost', 'Cost Price']
                    stock_fields = ['الكمية', 'stock', 'quantity', 'Stock', 'Quantity']
                    
                    # البحث عن اسم المنتج
                    product_name = None
                    for field in name_fields:
                        if field in row and row[field].strip():
                            product_name = row[field].strip()
                            break
                    
                    if not product_name:
                        errors.append(f"الصف {row_num}: اسم المنتج مطلوب")
                        error_count += 1
                        continue
                    
                    product_data['name'] = product_name
                    
                    # البحث عن كود المنتج
                    for field in code_fields:
                        if field in row and row[field].strip():
                            product_data['code'] = row[field].strip()
                            break
                    
                    # البحث عن السعر
                    for field in price_fields:
                        if field in row and row[field].strip():
                            try:
                                product_data['price'] = float(row[field].strip())
                            except ValueError:
                                pass
                            break
                    
                    # البحث عن التكلفة
                    for field in cost_fields:
                        if field in row and row[field].strip():
                            try:
                                product_data['cost'] = float(row[field].strip())
                            except ValueError:
                                pass
                            break
                    
                    # البحث عن الكمية
                    for field in stock_fields:
                        if field in row and row[field].strip():
                            try:
                                product_data['stock'] = int(float(row[field].strip()))
                            except ValueError:
                                pass
                            break
                    
                    # إضافة الحقول الأخرى
                    if 'الوصف' in row:
                        product_data['description'] = row['الوصف'].strip()
                    elif 'description' in row:
                        product_data['description'] = row['description'].strip()
                    
                    if 'الفئة' in row:
                        product_data['category'] = row['الفئة'].strip()
                    elif 'category' in row:
                        product_data['category'] = row['category'].strip()
                    
                    # التحقق من وجود المنتج
                    existing_product = None
                    if 'code' in product_data:
                        existing_product = ProductModel.get_product_by_code(product_data['code'])
                    
                    if existing_product:
                        # تحديث المنتج الموجود
                        ProductModel.update_product(existing_product['id'], product_data)
                        updated_count += 1
                        print(f"تم تحديث المنتج: {product_name}")
                    else:
                        # إضافة منتج جديد
                        # إنشاء كود تلقائي إذا لم يكن موجود
                        if 'code' not in product_data or not product_data['code']:
                            import random
                            product_data['code'] = f"AUTO_{random.randint(1000, 9999)}"
                        
                        # تعيين قيم افتراضية
                        product_data.setdefault('price', 0)
                        product_data.setdefault('cost', 0)
                        product_data.setdefault('stock', 0)
                        product_data.setdefault('min_quantity', 1)
                        product_data.setdefault('description', '')
                        product_data.setdefault('category', 'غير مصنف')
                        product_data.setdefault('product_type', 'physical')
                        product_data.setdefault('is_favorite', 0)
                        
                        ProductModel.add_product(product_data)
                        imported_count += 1
                        print(f"تم إضافة المنتج: {product_name}")
                        
                except Exception as e:
                    errors.append(f"الصف {row_num}: {str(e)}")
                    error_count += 1
        
        # عرض نتائج الاستيراد
        print(f"\nنتائج استيراد المنتجات:")
        print(f"المنتجات الجديدة: {imported_count}")
        print(f"المنتجات المحدثة: {updated_count}")
        print(f"الأخطاء: {error_count}")
        
        if errors:
            print("تفاصيل الأخطاء:")
            for error in errors:
                print(f"  - {error}")
        
        return error_count == 0
        
    except Exception as e:
        print(f"خطأ في استيراد المنتجات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("بدء اختبار استيراد البيانات...")
    
    # اختبار استيراد المنتجات
    products_test = test_import_products()
    
    # النتائج
    print("\n" + "="*50)
    print("نتائج الاختبار:")
    print(f"استيراد المنتجات: {'نجح' if products_test else 'فشل'}")
    
    if products_test:
        print("جميع الاختبارات نجحت! ✅")
        return True
    else:
        print("بعض الاختبارات فشلت! ❌")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
