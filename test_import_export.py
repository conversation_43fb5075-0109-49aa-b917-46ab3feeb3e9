#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف الاستيراد والتصدير
"""

import sys
import os
import csv
import tempfile

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.database import db
from models.products import ProductModel
from models.customers import CustomerModel

def test_export_products():
    """اختبار تصدير المنتجات"""
    print("اختبار تصدير المنتجات...")
    
    # الاتصال بقاعدة البيانات
    if not db.connect():
        print("فشل الاتصال بقاعدة البيانات")
        return False
    
    # الحصول على المنتجات
    products = ProductModel.get_all_products()
    print(f"عدد المنتجات: {len(products)}")
    
    if not products:
        print("لا توجد منتجات للتصدير")
        return True
    
    # إنشاء ملف مؤقت للتصدير
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as temp_file:
        fieldnames = ['الرقم', 'الكود', 'اسم المنتج', 'الوصف', 'الفئة', 'سعر البيع', 'سعر التكلفة', 'الكمية', 'الحد الأدنى', 'نوع المنتج', 'مفضل']
        writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
        
        # كتابة العناوين
        writer.writeheader()
        
        # كتابة بيانات المنتجات
        for product in products:
            writer.writerow({
                'الرقم': product.get('id', ''),
                'الكود': product.get('code', ''),
                'اسم المنتج': product.get('name', ''),
                'الوصف': product.get('description', ''),
                'الفئة': product.get('category', ''),
                'سعر البيع': product.get('price', 0),
                'سعر التكلفة': product.get('cost', 0),
                'الكمية': product.get('stock', 0),
                'الحد الأدنى': product.get('min_quantity', 1),
                'نوع المنتج': product.get('product_type', 'physical'),
                'مفضل': 'نعم' if product.get('is_favorite') else 'لا'
            })
        
        temp_filename = temp_file.name
    
    print(f"تم تصدير المنتجات إلى: {temp_filename}")
    
    # التحقق من الملف
    if os.path.exists(temp_filename):
        file_size = os.path.getsize(temp_filename)
        print(f"حجم الملف: {file_size} بايت")
        
        # قراءة الملف للتحقق من المحتوى
        with open(temp_filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
            print(f"عدد الأسطر في الملف: {len(lines)}")
        
        # حذف الملف المؤقت
        os.unlink(temp_filename)
        return True
    else:
        print("فشل في إنشاء الملف")
        return False

def test_export_customers():
    """اختبار تصدير العملاء"""
    print("\nاختبار تصدير العملاء...")
    
    # الحصول على العملاء
    customers = CustomerModel.get_all_customers()
    print(f"عدد العملاء: {len(customers)}")
    
    if not customers:
        print("لا توجد عملاء للتصدير")
        return True
    
    # إنشاء ملف مؤقت للتصدير
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as temp_file:
        fieldnames = ['الرقم', 'اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'إجمالي المشتريات', 'آخر زيارة', 'عدد الزيارات']
        writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
        
        # كتابة العناوين
        writer.writeheader()
        
        # كتابة بيانات العملاء
        for customer in customers:
            writer.writerow({
                'الرقم': customer.get('id', ''),
                'اسم العميل': customer.get('name', ''),
                'رقم الهاتف': customer.get('phone', ''),
                'البريد الإلكتروني': customer.get('email', ''),
                'العنوان': customer.get('address', ''),
                'إجمالي المشتريات': customer.get('total_purchases', 0),
                'آخر زيارة': customer.get('last_purchase', ''),
                'عدد الزيارات': customer.get('visit_count', 0)
            })
        
        temp_filename = temp_file.name
    
    print(f"تم تصدير العملاء إلى: {temp_filename}")
    
    # التحقق من الملف
    if os.path.exists(temp_filename):
        file_size = os.path.getsize(temp_filename)
        print(f"حجم الملف: {file_size} بايت")
        
        # قراءة الملف للتحقق من المحتوى
        with open(temp_filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
            print(f"عدد الأسطر في الملف: {len(lines)}")
        
        # حذف الملف المؤقت
        os.unlink(temp_filename)
        return True
    else:
        print("فشل في إنشاء الملف")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("بدء اختبار وظائف الاستيراد والتصدير...")
    
    # اختبار تصدير المنتجات
    products_test = test_export_products()
    
    # اختبار تصدير العملاء
    customers_test = test_export_customers()
    
    # النتائج
    print("\n" + "="*50)
    print("نتائج الاختبار:")
    print(f"تصدير المنتجات: {'نجح' if products_test else 'فشل'}")
    print(f"تصدير العملاء: {'نجح' if customers_test else 'فشل'}")
    
    if products_test and customers_test:
        print("جميع الاختبارات نجحت! ✅")
        return True
    else:
        print("بعض الاختبارات فشلت! ❌")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
