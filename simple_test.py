#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from models.database import db
    from models.products import ProductModel
    print("تم استيراد النماذج بنجاح")
    
    # الاتصال بقاعدة البيانات
    if db.connect():
        print("تم الاتصال بقاعدة البيانات بنجاح")
        
        # الحصول على المنتجات
        products = ProductModel.get_all_products()
        print(f"عدد المنتجات: {len(products)}")
        
        # اختبار البحث بالكود
        if products:
            first_product = products[0]
            code = first_product.get('code')
            if code:
                found_product = ProductModel.get_product_by_code(code)
                if found_product:
                    print(f"تم العثور على المنتج بالكود: {code}")
                else:
                    print(f"لم يتم العثور على المنتج بالكود: {code}")
        
        print("الاختبار نجح!")
    else:
        print("فشل الاتصال بقاعدة البيانات")
        
except Exception as e:
    print(f"خطأ: {str(e)}")
    import traceback
    traceback.print_exc()
